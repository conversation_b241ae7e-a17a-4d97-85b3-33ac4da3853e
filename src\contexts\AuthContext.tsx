import { clearAuth<PERSON><PERSON>, getC<PERSON>ie, get<PERSON><PERSON><PERSON><PERSON><PERSON>, isTokenExpired, setAuthData } from '@/helpers/auth';
import { login as apiLogin } from '@/services/authenticationService';
import React, { createContext, ReactNode, useContext, useEffect, useReducer } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'teacher' | 'student';
  avatar?: string;
  permissions?: string[];
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_FAILURE' }
  | { type: 'LOGOUT' }
  | { type: 'RESTORE_AUTH'; payload: User };

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true, // Start with loading true
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  console.log('Auth reducer action:', action.type, state.isAuthenticated);
  
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, isLoading: true };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
      };
    case 'LOGIN_FAILURE':
      return { ...state, isLoading: false, isAuthenticated: false, user: null };
    case 'LOGOUT':
      return { ...initialState, isLoading: false };
    case 'RESTORE_AUTH':
      console.log('Restoring auth with user:', action.payload);
      return { 
        ...state, 
        isLoading: false, 
        isAuthenticated: true,
        user: action.payload 
      };
    default:
      return state;
  }
};

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication status when the app loads
  useEffect(() => {
    const checkAuth = async () => {
      console.log('Checking authentication...');
      try {
        // Kiểm tra token
        const token = getCookie('authToken');
        console.log('Token found:', !!token);
        
        if (!token) {
          console.log('No token found');
          dispatch({ type: 'LOGIN_FAILURE' });
          return;
        }

        // Kiểm tra token có hợp lệ không
        const tokenExpired = isTokenExpired(token);
        console.log('Token expired:', tokenExpired);
        
        if (tokenExpired) {
          console.log('Token expired');
          clearAuthData();
          dispatch({ type: 'LOGIN_FAILURE' });
          return;
        }

        // Lấy thông tin user từ cookie
        const user = getCurrentUser();
        console.log('User from cookie:', user);
        
        if (!user) {
          console.log('No user data found');
          dispatch({ type: 'LOGIN_FAILURE' });
          return;
        }

        console.log('Valid session found, user:', user);
        dispatch({ type: 'RESTORE_AUTH', payload: user });
      } catch (error) {
        console.error('Error checking authentication:', error);
        dispatch({ type: 'LOGIN_FAILURE' });
      }
    };

    checkAuth();
  }, []);

  // Setup token expiration listener
  useEffect(() => {
    console.log('222222222222222222222222222222222222222222222222222223335353535');
    
    const token = getCookie('authToken');
     console.log('222222222222222222222222222222222222222222222222222223335353535', token);
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const expirationTime = payload.exp * 1000;
        const currentTime = Date.now();
        const timeUntilExpiration = expirationTime - currentTime;

        if (timeUntilExpiration > 0) {
          const logoutTimer = setTimeout(() => {
            handleLogout();
          }, timeUntilExpiration);

          // Cleanup timer on unmount
          return () => clearTimeout(logoutTimer);
        } else {
          // Token already expired
          handleLogout();
        }
      } catch (error) {
        console.error('Error setting up token expiration check:', error);
      }
    }
  }, [state.isAuthenticated]);

  const login = async (email: string, password: string) => {
    dispatch({ type: 'LOGIN_START' });
    
    try {
      // Use the login function from authenticationService
      const response = await apiLogin({ email, password });
      console.log('response response', response);
      
      // Extract token and user data from response
      // const { token, user } = response;
      
      // Store auth data in cookies
      setAuthData(response.data.accessToken, response.data.user);
      
      // Update state
      dispatch({ type: 'LOGIN_SUCCESS', payload: response.data.user });
    } catch (error) {
      console.error('Login error:', error);
      dispatch({ type: 'LOGIN_FAILURE' });
      throw error;
    }
  };

  const handleLogout = () => {
    clearAuthData();
    dispatch({ type: 'LOGOUT' });
    window.location.href = '/login';
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout: handleLogout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
