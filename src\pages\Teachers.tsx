import { Edit, Eye, FileDown, MoreHorizontal, Trash } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import DataTable from '../components/common/DataTable';

interface Teacher {
  id: string;
  name: string;
  email: string;
  phone: string;
  specialty: string;
  classes: string;
  status: string;
  startDate: string;
}

const ActionMenu = ({ open, anchorRef, onClose }: any) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });

  React.useEffect(() => {
    if (open && anchorRef.current) {
      const rect = anchorRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY + 4, // 4px margin
        left: rect.right + window.scrollX - 180, // 180 là chiều rộng menu
      });
    }
  }, [open, anchorRef]);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        anchorRef.current &&
        !anchorRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };
    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open, onClose, anchorRef]);

  if (!open) return null;

  return createPortal(
    <div
      ref={menuRef}
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        zIndex: 9999,
        background: '#fff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
        borderRadius: 8,
        minWidth: 180,
        padding: 8,
      }}
    >
      <div className="font-semibold text-gray-700 px-4 py-2">Thao tác</div>
      <button className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
        <Eye className="mr-2 h-4 w-4" /> Xem chi tiết
      </button>
      <button className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
        <Edit className="mr-2 h-4 w-4" /> Chỉnh sửa
      </button>
      <button className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
        <FileDown className="mr-2 h-4 w-4" /> Xuất thông tin
      </button>
      <button className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
        <Trash className="mr-2 h-4 w-4" /> Xóa
      </button>
    </div>,
    document.body
  );
};

const Teachers: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string>>({});
  
  // Định nghĩa các bộ lọc
  const filters = [
    {
      id: "status",
      label: "Tất cả trạng thái",
      options: [
        { value: "active", label: "Đang dạy" },
        { value: "onleave", label: "Nghỉ phép" },
        { value: "retired", label: "Đã nghỉ việc" }
      ]
    },
    {
      id: "specialty",
      label: "Tất cả chuyên môn",
      options: [
        { value: "it", label: "Công nghệ thông tin" },
        { value: "accounting", label: "Kế toán" },
        { value: "design", label: "Thiết kế" },
        { value: "cooking", label: "Nấu ăn" },
        { value: "hairstyle", label: "Làm tóc" },
      ]
    },
    {
      id: "specialty",
      label: "Tất cả chuyên môn",
      options: [
        { value: "it", label: "Công nghệ thông tin" },
        { value: "accounting", label: "Kế toán" },
        { value: "design", label: "Thiết kế" },
        { value: "cooking", label: "Nấu ăn" },
        { value: "hairstyle", label: "Làm tóc" },
      ]
    }
  ];

  // Xử lý thay đổi bộ lọc
  const handleFilterChange = (filterId: string, value: string) => {
    setSelectedFilters(prev => ({
      ...prev,
      [filterId]: value
    }));
    // Thêm logic lọc dữ liệu ở đây
  };

  const teachers: Teacher[] = [
    {
      id: 'TCH001',
      name: 'Nguyễn Văn A',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K15',
      status: 'Đang dạy',
      startDate: '01/01/2020',
    },
    {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
        {
      id: 'TCH002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '**********',
      specialty: 'Công nghệ thông tin',
      classes: 'CNTT-K14',
      status: 'Đang dạy',
      startDate: '01/06/2020',
    },
    // Add more teachers as needed
  ];

  const columns = [
    { key: 'id', header: 'Mã số' },
    { 
      key: 'name', 
      header: 'Họ và tên',
      render: (teacher: Teacher) => (
        <div className="flex items-center">
          <div className="h-10 w-10 flex-shrink-0">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-sm font-medium text-blue-700">
                {teacher.name.split(' ').pop()?.charAt(0)}
              </span>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{teacher.name}</div>
            <div className="text-sm text-gray-500">{teacher.email}</div>
          </div>
        </div>
      )
    },
    { key: 'phone', header: 'Số điện thoại' },
    { key: 'specialty', header: 'Chuyên môn' },
    { key: 'classes', header: 'Lớp phụ trách' },
    { 
      key: 'status', 
      header: 'Trạng thái',
      render: (teacher: Teacher) => (
        <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
          teacher.status === 'Đang dạy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {teacher.status}
        </span>
      )
    },
    { key: 'startDate', header: 'Ngày bắt đầu' },
  ];

  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const actionRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});
  const [page, setPage] = React.useState(1);
  const pageSize = 2; // số dòng/trang
  const allData = teachers; // mảng dữ liệu gốc

  const pagedData = React.useMemo(() => {
    const start = (page - 1) * pageSize;
    return allData.slice(start, start + pageSize);
  }, [allData, page, pageSize]);

  const renderActions = (teacher: Teacher) => {
    const ref = (el: HTMLButtonElement | null) => {
      actionRefs.current[teacher.id] = el;
    };
    return (
      <div className="relative">
        <button
          ref={ref}
          className="text-gray-400 hover:text-gray-500"
          onClick={() => setOpenMenuId(openMenuId === teacher.id ? null : teacher.id)}
        >
          <MoreHorizontal className="h-5 w-5" />
        </button>
        <ActionMenu
          open={openMenuId === teacher.id}
          anchorRef={{ current: actionRefs.current[teacher.id] }}
          onClose={() => setOpenMenuId(null)}
        />
      </div>
    );
  };

  return (
    <DataTable
      title="Quản lý giáo viên"
      description="Xem và quản lý danh sách giáo viên, phân công và thông tin chuyên môn"
      data={pagedData} // chỉ truyền dữ liệu của trang hiện tại
      columns={columns}
      keyField="id"
      searchPlaceholder="Tìm kiếm giáo viên theo tên, mã số, chuyên môn..."
      onAddNew={() => console.log('Add new teacher')}
      addNewLabel="Thêm giáo viên"
      onExport={() => console.log('Export teachers')}
      exportLabel="Xuất giáo viên"
      renderActions={renderActions}
      page={page}
      pageSize={pageSize}
      total={allData.length}
      onPageChange={setPage}
      // Thêm các props cho SearchBar
      searchFilters={filters}
      selectedFilters={selectedFilters}
      onFilterChange={handleFilterChange}
    />
  );
};

export default Teachers;
