import { Plus } from 'lucide-react';
import React, { useState } from 'react';
import SearchBar from '../components/common/SearchBar';

const Students: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string>>({});
  
  // Định nghĩa các bộ lọc
  const filters = [
    {
      id: "status",
      label: "Tất cả trạng thái",
      options: [
        { value: "active", label: "Đang học" },
        { value: "pending", label: "Đăng ký học" },
        { value: "graduated", label: "Tốt nghiệp" },
        { value: "suspended", label: "Nghỉ học" },
        { value: "expelled", label: "Bỏ học" },
      ]
    },
    {
      id: "major",
      label: "Tất cả ngành nghề",
      options: [
        { value: "it", label: "Công nghệ thông tin" },
        { value: "accounting", label: "<PERSON><PERSON> toán" },
        { value: "design", label: "<PERSON>hiế<PERSON> kế" },
        { value: "cooking", label: "Nấu ăn" },
        { value: "hairstyle", label: "<PERSON>à<PERSON> tóc" },
      ]
    }
  ];

  // Xử lý thay đổi bộ lọc
  const handleFilterChange = (filterId: string, value: string) => {
    setSelectedFilters(prev => ({
      ...prev,
      [filterId]: value
    }));
  };

  const students = [
    {
      id: 1,
      name: 'Nguyễn Văn An',
      email: '<EMAIL>',
      phone: '**********',
      major: 'Công nghệ thông tin',
      status: 'Đang học',
      joinDate: '2024-01-15',
    },
    {
      id: 2,
      name: 'Trần Thị Bình',
      email: '<EMAIL>',
      phone: '**********',
      major: 'Kinh tế',
      status: 'Tốt nghiệp',
      joinDate: '2023-09-10',
    },
    {
      id: 3,
      name: 'Lê Minh Cường',
      email: '<EMAIL>',
      phone: '**********',
      major: 'Ngoại ngữ',
      status: 'Đang học',
      joinDate: '2024-02-20',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Quản lý học viên</h1>
          <p className="text-gray-600">Danh sách và thông tin học viên</p>
        </div>
        <button className="flex items-center space-x-2 rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">
          <Plus className="h-4 w-4" />
          <span>Thêm học viên</span>
        </button>
      </div>

      {/* Filters and search */}
      <SearchBar
        searchPlaceholder="Tìm kiếm học viên theo tên, mã số, số điện thoại..."
        value={searchQuery}
        onChange={setSearchQuery}
        filters={filters}
        selectedFilters={selectedFilters}
        onFilterChange={handleFilterChange}
        fullWidth={true}
      />

      {/* Students table */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Học viên
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Liên hệ
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Ngành học
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Trạng thái
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Ngày nhập học
              </th>
              <th className="relative px-6 py-3">
                <span className="sr-only">Hành động</span>
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {students.map((student) => (
              <tr key={student.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="h-10 w-10 flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-700">
                          {student.name.charAt(0)}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {student.name}
                      </div>
                      <div className="text-sm text-gray-500">{student.email}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {student.phone}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {student.major}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                      student.status === 'Đang học'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-blue-100 text-blue-800'
                    }`}
                  >
                    {student.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {new Date(student.joinDate).toLocaleDateString('vi-VN')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button className="text-blue-600 hover:text-blue-900">
                    Chỉnh sửa
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Students;
