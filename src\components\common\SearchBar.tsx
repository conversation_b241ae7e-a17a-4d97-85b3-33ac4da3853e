import { cn } from '@/utils/utils';
import { Filter, Search, X } from 'lucide-react';
import React, { useState } from 'react';

interface FilterOption {
  id: string;
  label: string;
  options: { value: string; label: string }[];
}

interface SearchBarProps {
  searchPlaceholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  filters?: FilterOption[];
  onFilterChange?: (filterId: string, value: string) => void;
  selectedFilters?: Record<string, string>;
  fullWidth?: boolean;
}

const SearchBar: React.FC<SearchBarProps> = ({
  searchPlaceholder = "Tìm kiếm...",
  value = '',
  onChange,
  filters = [],
  onFilterChange,
  selectedFilters = {},
  fullWidth = false,
}) => {
  const [showFilters, setShowFilters] = useState(false);
  
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value);
  };

  const handleClearFilter = (filterId: string) => {
    if (onFilterChange) {
      onFilterChange(filterId, '');
    }
  };

  const hasActiveFilters = Object.values(selectedFilters).some(value => value !== '');

  return (
    <div className="w-full">
      {/* Search input and filters row */}
      <div className="flex items-center gap-4">
        {/* Search input - chiếm đúng 1/2 */}
        <div className={cn(
          "relative",
          filters.length > 0 ? "w-1/2" : "w-full"
        )}>
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            value={value}
            onChange={handleSearch}
            placeholder={searchPlaceholder}
            className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>

        {/* Filter dropdowns - chiếm 1/2 còn lại */}
        {filters.length > 0 && (
          <div className="hidden md:flex w-1/2 items-center gap-2">
            {filters.map(filter => (
              <div key={filter.id} className="relative flex-1">
                <select
                  className="w-full appearance-none rounded-lg border border-gray-300 bg-white py-2 pl-4 pr-10 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  value={selectedFilters[filter.id] || ''}
                  onChange={(e) => onFilterChange?.(filter.id, e.target.value)}
                >
                  <option value="">{filter.label}</option>
                  {filter.options.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg className="h-4 w-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            ))}

            {/* Filter button */}
            <button
              className={cn(
                "flex items-center justify-center rounded-lg border border-gray-300 bg-white px-3 py-2 text-gray-700 hover:bg-gray-50 flex-shrink-0",
                hasActiveFilters && "border-blue-500 text-blue-600"
              )}
            >
              <Filter className="h-4 w-4" />
            </button>
          </div>
        )}

        {/* Mobile filter button */}
        {filters.length > 0 && (
          <div className="md:hidden w-1/2 flex justify-end">
            <button
              className={cn(
                "flex items-center space-x-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50",
                hasActiveFilters && "border-blue-500 text-blue-600"
              )}
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4" />
              <span className="hidden sm:inline">Lọc</span>
              {hasActiveFilters && (
                <span className="inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                  {Object.values(selectedFilters).filter(v => v !== '').length}
                </span>
              )}
            </button>
          </div>
        )}
      </div>
      
      {/* Active filters display */}
      {hasActiveFilters && (
        <div className="mt-2 flex flex-wrap gap-2">
          {Object.entries(selectedFilters).map(([filterId, value]) => {
            if (!value) return null;
            
            const filterDef = filters.find(f => f.id === filterId);
            const optionLabel = filterDef?.options.find(o => o.value === value)?.label;
            
            if (!filterDef || !optionLabel) return null;
            
            return (
              <div 
                key={filterId}
                className="inline-flex items-center rounded-full bg-blue-50 px-3 py-1 text-sm text-blue-700"
              >
                <span className="mr-1 text-xs text-blue-500">{filterDef.label.replace('Tất cả ', '')}:</span>
                <span>{optionLabel}</span>
                <button 
                  className="ml-1 rounded-full p-1 hover:bg-blue-100"
                  onClick={() => handleClearFilter(filterId)}
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            );
          })}
          
          <button 
            className="text-sm text-blue-600 hover:underline"
            onClick={() => {
              if (onFilterChange) {
                Object.keys(selectedFilters).forEach(key => {
                  onFilterChange(key, '');
                });
              }
            }}
          >
            Xóa tất cả
          </button>
        </div>
      )}
      
      {/* Mobile filter panel */}
      {showFilters && (
        <div className="md:hidden mt-4 space-y-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">Bộ lọc</h3>
            <button 
              className="text-gray-400 hover:text-gray-500"
              onClick={() => setShowFilters(false)}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
          
          <div className="space-y-3">
            {filters.map(filter => (
              <div key={filter.id} className="space-y-1">
                <label className="text-xs font-medium text-gray-700">{filter.label}</label>
                <select
                  className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-3 pr-10 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  value={selectedFilters[filter.id] || ''}
                  onChange={(e) => onFilterChange?.(filter.id, e.target.value)}
                >
                  <option value="">{filter.label}</option>
                  {filter.options.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            ))}
          </div>
          
          <div className="flex justify-end space-x-2 pt-2">
            <button 
              className="rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              onClick={() => {
                if (onFilterChange) {
                  Object.keys(selectedFilters).forEach(key => {
                    onFilterChange(key, '');
                  });
                }
              }}
            >
              Đặt lại
            </button>
            <button 
              className="rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
              onClick={() => setShowFilters(false)}
            >
              Áp dụng
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchBar;


