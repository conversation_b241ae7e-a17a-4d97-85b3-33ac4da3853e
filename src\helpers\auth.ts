// import { USER_ROLE, UserRoleType } from "@/constants/constants";

// utils/cookieAuth.ts
export interface User {
  id: string;
  permissions: string[];
  // role: UserRoleType;
  username: string;
  description?: string;
}

// Cookie utilities
export const setCookie = (
  name: string,
  value: string,
  options: {
    maxAge?: number;
    secure?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
    path?: string;
  } = {}
): void => {
  try {
    const {
      maxAge = 7 * 24 * 60 * 60, // 7 days default
      secure = window.location.protocol === 'https:',
      sameSite = 'lax', // Sử dụng 'lax' để hoạt động tốt với refresh
      path = '/',
    } = options;

    let cookieString = `${name}=${encodeURIComponent(value)}`;
    cookieString += `; Max-Age=${maxAge}`;
    cookieString += `; Path=${path}`;

    if (sameSite) {
      cookieString += `; SameSite=${sameSite}`;
    }

    if (secure) {
      cookieString += '; Secure';
    }

    console.log(`Setting cookie: ${name} with value length: ${value.length}`);
    document.cookie = cookieString;

    // Verify cookie was set
    setTimeout(() => {
      const verifyValue = getCookie(name);
      console.log(`Verify cookie ${name} was set:`, !!verifyValue);
    }, 100);
  } catch (error) {
    console.error('Error setting cookie:', error);
  }
};

export const getCookie = (name: string): string | null => {
  try {
    const nameEQ = name + '=';
    const ca = document.cookie.split(';');
    console.log('All cookies:', document.cookie);

    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) {
        const value = decodeURIComponent(c.substring(nameEQ.length, c.length));
        console.log(`Found cookie ${name}:`, !!value);
        return value;
      }
    }
    console.log(`Cookie ${name} not found`);
    return null;
  } catch (error) {
    console.error(`Error getting cookie ${name}:`, error);
    return null;
  }
};

export const deleteCookie = (name: string, path: string = '/'): void => {
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; SameSite=strict`;
};

// Auth-specific functions
export const getCurrentUser = (): User | null => {
  try {
    const userData = getCookie('userData');
    if (!userData) {
      console.log('No user data found in cookie');
      return null;
    }

    const user = JSON.parse(decodeURIComponent(userData));
    console.log('User data retrieved from cookie:', user);

    // Kiểm tra xem user có đủ thông tin cần thiết không
    if (!user.id) {
      console.error('User data is missing required fields');
      return null;
    }

    return user;
  } catch (error) {
    console.error('Error parsing user data:', error);
    return null;
  }
};

export const setAuthData = (token: string, user: User): void => {
  console.log('2222222222222222222222222222222222222222224555', token);

  try {
    // Kiểm tra token và user
    if (!token || !user || !user.id) {
      console.error('Invalid auth data', { token, user });
      return;
    }

    // Lưu token vào cookie với thời gian dài hơn
    setCookie('authToken', token, {
      maxAge: 7 * 24 * 60 * 60, // 7 days
      secure: window.location.protocol === 'https:',
      sameSite: 'lax', // Thay đổi từ 'strict' sang 'lax' để hoạt động tốt hơn với redirects
      path: '/',
    });

    // Lưu user data vào cookie
    const userDataString = JSON.stringify(user);
    console.log('Saving user data to cookie:', userDataString);

    setCookie('userData', userDataString, {
      maxAge: 7 * 24 * 60 * 60, // 7 days
      secure: window.location.protocol === 'https:',
      sameSite: 'lax',
      path: '/',
    });

    // Log để debug
    console.log('Auth data set successfully:', { token, user });

    // Verify cookies were set
    setTimeout(() => {
      const savedToken = getCookie('authToken');
      const savedUserData = getCookie('userData');
      console.log('Verify auth data was saved:', {
        tokenSaved: !!savedToken,
        userDataSaved: !!savedUserData,
      });
    }, 100);
  } catch (error) {
    console.error('Error setting auth data:', error);
  }
};

export const clearAuthData = (): void => {
  deleteCookie('authToken');
  deleteCookie('userData');
};

export const isAuthenticated = (): boolean => {
  const token = getCookie('authToken');
  return !!token && !isTokenExpired(token);
};

export const isTokenExpired = (token: string): boolean => {
  try {
    // Kiểm tra token có đúng định dạng JWT không
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.error('Invalid token format');
      return true;
    }

    // Giải mã phần payload
    const payload = JSON.parse(atob(parts[1]));

    // Kiểm tra xem payload có chứa exp không
    if (!payload.exp) {
      console.error('Token does not contain expiration');
      return true;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    const isExpired = payload.exp < currentTime;

    console.log('Token expiration check:', {
      expiration: new Date(payload.exp * 1000).toISOString(),
      currentTime: new Date(currentTime * 1000).toISOString(),
      isExpired,
    });

    return isExpired;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
};

export const hasPermission = (permission: string): boolean => {
  const user = getCurrentUser();
  return user?.permissions.includes(permission) || false;
};

export const hasRole = (role: UserRoleType): boolean => {
  const user = getCurrentUser();
  return user?.role === role;
};

export const getUserRole = (): string | null => {
  const user = getCurrentUser();
  return user?.role || null;
};

export const getUserPermissions = (): string[] => {
  const user = getCurrentUser();
  return user?.permissions || [];
};

// API interceptor for automatic cookie handling
export const apiRequest = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  const defaultOptions: RequestInit = {
    credentials: 'include', // This ensures cookies are sent with requests
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(url, defaultOptions);

  // Check if token expired based on response
  if (response.status === 401) {
    clearAuthData();
    window.location.href = '/login';
  }

  return response;
};

// Setup auto logout when token expires
export const setupTokenExpirationCheck = (callback?: () => void): void => {
  const token = getCookie('authToken');
  if (!token) return;

  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();
    const timeUntilExpiration = expirationTime - currentTime;

    if (timeUntilExpiration > 0) {
      setTimeout(() => {
        clearAuthData();
        if (callback) {
          callback();
        } else {
          window.location.href = '/login';
        }
      }, timeUntilExpiration);
    }
  } catch (error) {
    console.error('Error setting up token expiration check:', error);
  }
};

export const logout = (): void => {
  // Clear authentication data from cookies
  clearAuthData();

  // Redirect to login page
  window.location.href = '/signin';
};

// Thêm hàm tiện ích để kiểm tra nhanh các role phổ biến
// export const isAdmin = (): boolean => {
//   return hasRole(USER_ROLE.ADMIN);
// };

// export const isModerator = (): boolean => {
//   return hasRole(USER_ROLE.VIEWER) || hasRole(USER_ROLE.ADMIN);
// };

// Thêm hàm kiểm tra session hiện tại
export const checkCurrentSession = (): boolean => {
  try {
    const token = getCookie('authToken');
    if (!token) {
      console.log('No token found');
      return false;
    }

    // Kiểm tra token có hợp lệ không
    if (isTokenExpired(token)) {
      console.log('Token expired');
      clearAuthData();
      return false;
    }

    // Kiểm tra user data
    const user = getCurrentUser();
    if (!user) {
      console.log('No user data found');
      return false;
    }

    console.log('Valid session found');
    return true;
  } catch (error) {
    console.error('Error checking session:', error);
    return false;
  }
};

export const checkSession = (): { isValid: boolean; user: User | null } => {
  try {
    console.log('Checking session...');

    // Kiểm tra token
    const token = getCookie('authToken');
    if (!token) {
      console.log('Session check: No token found');
      return { isValid: false, user: null };
    }

    // Kiểm tra token có hợp lệ không
    if (isTokenExpired(token)) {
      console.log('Session check: Token expired');
      clearAuthData();
      return { isValid: false, user: null };
    }

    // Lấy thông tin user từ cookie
    const user = getCurrentUser();
    if (!user) {
      console.log('Session check: No user data found');
      return { isValid: false, user: null };
    }

    console.log('Session check: Valid session found');
    return { isValid: true, user };
  } catch (error) {
    console.error('Error checking session:', error);
    return { isValid: false, user: null };
  }
};
