import { Filter, Search } from 'lucide-react';
import React from 'react';

interface FilterOption {
  label: string;
  value: string;
  options: { label: string; value: string }[];
}

interface SearchFiltersProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  searchPlaceholder?: string;
  filters?: FilterOption[];
  selectedFilters?: Record<string, string>;
  onFilterChange?: (filterKey: string, value: string) => void;
  variant?: 'full' | 'withFilters';
  showFilterButton?: boolean;
  onFilterButtonClick?: () => void;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  searchQuery,
  onSearchChange,
  searchPlaceholder = 'Tìm kiếm...',
  filters = [],
  selectedFilters = {},
  onFilterChange,
  variant = 'full',
  showFilterButton = true,
  onFilterButtonClick,
}) => {
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(e.target.value);
  };

  return (
    <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
      <div className={`flex items-center space-x-4 ${variant === 'full' ? 'w-full' : ''}`}>
        <div className={`relative ${variant === 'full' && !showFilterButton ? 'w-full' : 'w-64'}`}>
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearch}
            placeholder={searchPlaceholder}
            className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>
        
        {showFilterButton && (
          <button 
            className="flex items-center space-x-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
            onClick={onFilterButtonClick}
          >
            <Filter className="h-4 w-4" />
            <span>Lọc</span>
          </button>
        )}
      </div>

      {variant === 'withFilters' && filters.length > 0 && (
        <div className="flex flex-wrap items-center gap-2">
          {filters.map((filter) => (
            <div key={filter.value} className="min-w-[150px]">
              <select
                className="w-full rounded-lg border border-gray-300 bg-white py-2 px-3 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                value={selectedFilters[filter.value] || ''}
                onChange={(e) => onFilterChange?.(filter.value, e.target.value)}
              >
                <option value="">{filter.label}</option>
                {filter.options.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchFilters;